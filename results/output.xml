<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3.1 (Python 3.10.18 on linux)" generated="2025-08-07T15:38:55.795796" rpa="false" schemaversion="5">
<suite id="s1" name="Reward Bing" source="/home/<USER>/Desktop/project_local/reward_bing">
<suite id="s1-s1" name="Run" source="/home/<USER>/Desktop/project_local/reward_bing/Run">
<suite id="s1-s1-s1" name="Login" source="/home/<USER>/Desktop/project_local/reward_bing/Run/Login.robot">
<test id="s1-s1-s1-t1" name="Verify Login" line="10">
<kw name="Open Browser Keywords" owner="BrowserSetup">
<kw name="Get Chrome Options" owner="browser_options">
<msg time="2025-08-07T15:38:55.962658" level="INFO">Created temporary user data directory: /tmp/chrome_profile_r14fyyl0</msg>
<msg time="2025-08-07T15:38:55.962986" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x7026a36a8d90&gt;</msg>
<var>${chrome_options}</var>
<doc>Configure Chrome browser options for Docker/headless environments.</doc>
<status status="PASS" start="2025-08-07T15:38:55.961711" elapsed="0.001414"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-08-07T15:38:55.965160" level="INFO">Opening browser 'Chrome' to base url 'https://rewards.bing.com/'.</msg>
<arg>${BASE_URL}</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-08-07T15:38:55.963496" elapsed="3.819640"/>
</kw>
<doc>Open the browser</doc>
<status status="PASS" start="2025-08-07T15:38:55.959329" elapsed="3.824120"/>
</kw>
<kw name="Login User" owner="Login">
<kw name="Input Email" owner="Login">
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-08-07T15:38:59.785802" level="INFO">Typing text '<EMAIL>' into text field '//input[@id="i0116"]'.</msg>
<msg time="2025-08-07T15:38:59.921651" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-08-07T15:38:59.921994" level="FAIL">Element with locator '//input[@id="i0116"]' not found.</msg>
<arg>${INPUT_EMAIL}</arg>
<arg>${EMAIL}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="FAIL" start="2025-08-07T15:38:59.785317" elapsed="0.152128">Element with locator '//input[@id="i0116"]' not found.</status>
</kw>
<msg time="2025-08-07T15:38:59.937715" level="INFO">${result} = ('FAIL', 'Element with locator \'//input[@id="i0116"]\' not found.')</msg>
<var>${result}</var>
<arg>Input Text</arg>
<arg>${INPUT_EMAIL}</arg>
<arg>${EMAIL}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-08-07T15:38:59.784925" elapsed="0.152905"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-08-07T15:38:59.939073" level="INFO">Typing text '<EMAIL>' into text field '//input[@id="usernameEntry"]'.</msg>
<arg>${INPUT_EMAIL_2}</arg>
<arg>${EMAIL}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-08-07T15:38:59.938720" elapsed="0.141658"/>
</kw>
<arg>'${result[0]}' == 'FAIL'</arg>
<arg>Input Text</arg>
<arg>${INPUT_EMAIL_2}</arg>
<arg>${EMAIL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-08-07T15:38:59.938038" elapsed="0.142552"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-08-07T15:39:00.081059" level="INFO">Clicking button '//button[@data-testid="primaryButton"]'.</msg>
<arg>${NEXT_BUTTON}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-08-07T15:39:00.080760" elapsed="0.060879"/>
</kw>
<arg>${EMAIL}</arg>
<status status="PASS" start="2025-08-07T15:38:59.784464" elapsed="0.357943"/>
</kw>
<kw name="Use Password" owner="Login">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-08-07T15:39:00.146367" level="INFO">Clicking element '//span[@role="button"]'.</msg>
<arg>${OTHER_WAY}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-08-07T15:39:00.145509" elapsed="1.841825"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<msg time="2025-08-07T15:39:07.287438" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-3.png"&gt;&lt;img src="selenium-screenshot-3.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-08-07T15:39:07.287823" level="FAIL">Element '//span[contains(text(), "Use your password")]' not visible after 5 seconds.</msg>
<arg>${USE_PASSWORD}</arg>
<arg>5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="FAIL" start="2025-08-07T15:39:01.987634" elapsed="5.308161">Element '//span[contains(text(), "Use your password")]' not visible after 5 seconds.</status>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${USE_PASSWORD}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-08-07T15:39:07.296144" elapsed="0.000165"/>
</kw>
<status status="FAIL" start="2025-08-07T15:39:00.143647" elapsed="7.152836">Element '//span[contains(text(), "Use your password")]' not visible after 5 seconds.</status>
</kw>
<kw name="Input Password" owner="Login">
<arg>${PASSWORD}</arg>
<status status="NOT RUN" start="2025-08-07T15:39:07.296709" elapsed="0.000105"/>
</kw>
<kw name="Not Stay Login" owner="Login">
<status status="NOT RUN" start="2025-08-07T15:39:07.296966" elapsed="0.000087"/>
</kw>
<arg>${USERNAME}</arg>
<arg>${PASSWORD}</arg>
<status status="FAIL" start="2025-08-07T15:38:59.783844" elapsed="7.513317">Element '//span[contains(text(), "Use your password")]' not visible after 5 seconds.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-08-07T15:39:07.297354" elapsed="0.000135"/>
</kw>
<doc>Test case to verify valid login with user admin</doc>
<status status="FAIL" start="2025-08-07T15:38:55.953914" elapsed="11.343821">Element '//span[contains(text(), "Use your password")]' not visible after 5 seconds.</status>
</test>
<status status="FAIL" start="2025-08-07T15:38:55.848599" elapsed="11.458682"/>
</suite>
<status status="FAIL" start="2025-08-07T15:38:55.838044" elapsed="11.476472"/>
</suite>
<status status="FAIL" start="2025-08-07T15:38:55.799444" elapsed="11.517566"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="1" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="Reward Bing" id="s1" pass="0" fail="1" skip="0">Reward Bing</stat>
<stat name="Run" id="s1-s1" pass="0" fail="1" skip="0">Reward Bing.Run</stat>
<stat name="Login" id="s1-s1-s1" pass="0" fail="1" skip="0">Reward Bing.Run.Login</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
