<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3.1 (Python 3.10.18 on linux)" generated="2025-08-08T09:52:01.916376" rpa="false" schemaversion="5">
<suite id="s1" name="EarnPoint" source="/home/<USER>/Desktop/project_local/reward_bing/Run/EarnPoint.robot">
<test id="s1-t1" name="Earn Point Click A Element" line="11">
<kw name="Open Browser Keywords" owner="BrowserSetup">
<kw name="Get Chrome Options" owner="browser_options">
<msg time="2025-08-08T09:52:02.113349" level="INFO">Created temporary user data directory: /tmp/chrome_profile_p3pzjst9</msg>
<msg time="2025-08-08T09:52:02.114294" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x7dd03fd2a0e0&gt;</msg>
<var>${chrome_options}</var>
<doc>Configure Chrome browser options for Docker/headless environments.</doc>
<status status="PASS" start="2025-08-08T09:52:02.112624" elapsed="0.001768"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-08-08T09:52:02.115193" level="INFO">Opening browser 'Chrome' to base url 'https://rewards.bing.com/'.</msg>
<arg>${BASE_URL}</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-08-08T09:52:02.114663" elapsed="5.148391"/>
</kw>
<doc>Open the browser</doc>
<status status="PASS" start="2025-08-08T09:52:02.111527" elapsed="5.151809"/>
</kw>
<kw name="Login User" owner="Login">
<kw name="Input Email" owner="Login">
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-08-08T09:52:07.266221" level="INFO">Typing text '<EMAIL>' into text field '//input[@id="i0116"]'.</msg>
<msg time="2025-08-08T09:52:07.391042" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-7.png"&gt;&lt;img src="selenium-screenshot-7.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-08-08T09:52:07.391520" level="FAIL">Element with locator '//input[@id="i0116"]' not found.</msg>
<arg>${INPUT_EMAIL}</arg>
<arg>${EMAIL}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="FAIL" start="2025-08-08T09:52:07.265714" elapsed="0.129493">Element with locator '//input[@id="i0116"]' not found.</status>
</kw>
<msg time="2025-08-08T09:52:07.395586" level="INFO">${result} = ('FAIL', 'Element with locator \'//input[@id="i0116"]\' not found.')</msg>
<var>${result}</var>
<arg>Input Text</arg>
<arg>${INPUT_EMAIL}</arg>
<arg>${EMAIL}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-08-08T09:52:07.265263" elapsed="0.130456"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-08-08T09:52:07.397702" level="INFO">Typing text '<EMAIL>' into text field '//input[@id="usernameEntry"]'.</msg>
<arg>${INPUT_EMAIL_2}</arg>
<arg>${EMAIL}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-08-08T09:52:07.397262" elapsed="0.141885"/>
</kw>
<arg>'${result[0]}' == 'FAIL'</arg>
<arg>Input Text</arg>
<arg>${INPUT_EMAIL_2}</arg>
<arg>${EMAIL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-08-08T09:52:07.396009" elapsed="0.143784"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-08-08T09:52:07.540770" level="INFO">Clicking button '//button[@data-testid="primaryButton"]'.</msg>
<arg>${NEXT_BUTTON}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-08-08T09:52:07.540147" elapsed="0.052477"/>
</kw>
<arg>${EMAIL}</arg>
<status status="PASS" start="2025-08-08T09:52:07.264565" elapsed="0.328544"/>
</kw>
<kw name="Use Password" owner="Login">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${OTHER_WAY}</arg>
<arg>5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-08-08T09:52:07.594726" elapsed="0.019205"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-08-08T09:52:07.614528" level="INFO">Clicking element '//span[@role="button"]'.</msg>
<arg>${OTHER_WAY}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-08-08T09:52:07.614212" elapsed="2.421834"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<msg time="2025-08-08T09:52:12.982208" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x7dd03fd5bb50&gt;: Failed to establish a new connection: [Errno 111] Connection refused')': /session/977ac7032333393131da467144e46551/screenshot</msg>
<msg time="2025-08-08T09:52:12.983135" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x7dd03fd5b820&gt;: Failed to establish a new connection: [Errno 111] Connection refused')': /session/977ac7032333393131da467144e46551/screenshot</msg>
<msg time="2025-08-08T09:52:12.983774" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x7dd03fd5beb0&gt;: Failed to establish a new connection: [Errno 111] Connection refused')': /session/977ac7032333393131da467144e46551/screenshot</msg>
<msg time="2025-08-08T09:52:12.984374" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=50885): Max retries exceeded with url: /session/977ac7032333393131da467144e46551/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x7dd03fc240d0&gt;: Failed to establish a new connection: [Errno 111] Connection refused'))</msg>
<msg time="2025-08-08T09:52:12.984576" level="INFO">robot: 2025-08-08 09:52:12 UTC pid: 29930 - Write from robot to dap (_RobotTargetComm) - EXCEPTION - robocorp_ls_core.debug_adapter_core.debug_adapter_threads
Error writing message.</msg>
<msg time="2025-08-08T09:52:12.984801" level="FAIL">BrokenPipeError: [Errno 32] Broken pipe</msg>
<arg>${USE_PASSWORD}</arg>
<arg>5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="FAIL" start="2025-08-08T09:52:10.036883" elapsed="2.953967">BrokenPipeError: [Errno 32] Broken pipe</status>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${USE_PASSWORD}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-08-08T09:52:12.991129" elapsed="0.000098"/>
</kw>
<status status="FAIL" start="2025-08-08T09:52:07.593740" elapsed="5.397616">BrokenPipeError: [Errno 32] Broken pipe</status>
</kw>
<kw name="Input Password" owner="Login">
<arg>${PASSWORD}</arg>
<status status="NOT RUN" start="2025-08-08T09:52:12.991546" elapsed="0.000086"/>
</kw>
<kw name="Not Stay Login" owner="Login">
<status status="NOT RUN" start="2025-08-08T09:52:12.991768" elapsed="0.000063"/>
</kw>
<arg>${USERNAME}</arg>
<arg>${PASSWORD}</arg>
<status status="FAIL" start="2025-08-08T09:52:07.263687" elapsed="5.728258">BrokenPipeError: [Errno 32] Broken pipe</status>
</kw>
<kw name="Click All Earn Links" owner="EarnPoint">
<arg>${A_CLICK_TO_EARN_5}</arg>
<status status="NOT RUN" start="2025-08-08T09:52:12.992300" elapsed="0.000111"/>
</kw>
<kw name="Click All Earn Links" owner="EarnPoint">
<arg>${A_CLICK_TO_EARN_10}</arg>
<status status="NOT RUN" start="2025-08-08T09:52:12.992673" elapsed="0.000103"/>
</kw>
<kw name="Click All Earn Links" owner="EarnPoint">
<arg>${A_CLICK_TO_EARN_15}</arg>
<status status="NOT RUN" start="2025-08-08T09:52:12.993074" elapsed="0.000104"/>
</kw>
<status status="FAIL" start="2025-08-08T09:52:02.110560" elapsed="10.882856">BrokenPipeError: [Errno 32] Broken pipe</status>
</test>
<status status="FAIL" start="2025-08-08T09:52:01.918118" elapsed="11.075858"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="1" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="EarnPoint" id="s1" pass="0" fail="1" skip="0">EarnPoint</stat>
</suite>
</statistics>
<errors>
<msg time="2025-08-08T09:52:12.982208" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x7dd03fd5bb50&gt;: Failed to establish a new connection: [Errno 111] Connection refused')': /session/977ac7032333393131da467144e46551/screenshot</msg>
<msg time="2025-08-08T09:52:12.983135" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x7dd03fd5b820&gt;: Failed to establish a new connection: [Errno 111] Connection refused')': /session/977ac7032333393131da467144e46551/screenshot</msg>
<msg time="2025-08-08T09:52:12.983774" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x7dd03fd5beb0&gt;: Failed to establish a new connection: [Errno 111] Connection refused')': /session/977ac7032333393131da467144e46551/screenshot</msg>
<msg time="2025-08-08T09:52:12.984374" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=50885): Max retries exceeded with url: /session/977ac7032333393131da467144e46551/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x7dd03fc240d0&gt;: Failed to establish a new connection: [Errno 111] Connection refused'))</msg>
</errors>
</robot>
