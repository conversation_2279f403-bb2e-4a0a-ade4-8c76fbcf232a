<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3.1 (Python 3.10.18 on linux)" generated="2025-08-08T09:57:23.957456" rpa="false" schemaversion="5">
<suite id="s1" name="EarnPoint" source="/home/<USER>/Desktop/project_local/reward_bing/Run/EarnPoint.robot">
<test id="s1-t1" name="Earn Point Click A Element" line="11">
<kw name="Open Browser Keywords" owner="BrowserSetup">
<kw name="Get Chrome Options" owner="browser_options">
<msg time="2025-08-08T09:57:24.303802" level="INFO">Created temporary user data directory: /tmp/chrome_profile_mxs_ejtq</msg>
<msg time="2025-08-08T09:57:24.305248" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x734404f2ab30&gt;</msg>
<var>${chrome_options}</var>
<doc>Configure Chrome browser options for Docker/headless environments.</doc>
<status status="PASS" start="2025-08-08T09:57:24.302865" elapsed="0.002578"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-08-08T09:57:24.307753" level="INFO">Opening browser 'Chrome' to base url 'https://rewards.bing.com/'.</msg>
<arg>${BASE_URL}</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-08-08T09:57:24.306063" elapsed="3.795045"/>
</kw>
<doc>Open the browser</doc>
<status status="PASS" start="2025-08-08T09:57:24.299922" elapsed="3.802369"/>
</kw>
<kw name="Login User" owner="Login">
<kw name="Input Email" owner="Login">
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-08-08T09:57:28.116649" level="INFO">Typing text '<EMAIL>' into text field '//input[@id="i0116"]'.</msg>
<msg time="2025-08-08T09:57:28.250475" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-9.png"&gt;&lt;img src="selenium-screenshot-9.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-08-08T09:57:28.251010" level="FAIL">Element with locator '//input[@id="i0116"]' not found.</msg>
<arg>${INPUT_EMAIL}</arg>
<arg>${EMAIL}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="FAIL" start="2025-08-08T09:57:28.114142" elapsed="0.139755">Element with locator '//input[@id="i0116"]' not found.</status>
</kw>
<msg time="2025-08-08T09:57:28.254543" level="INFO">${result} = ('FAIL', 'Element with locator \'//input[@id="i0116"]\' not found.')</msg>
<var>${result}</var>
<arg>Input Text</arg>
<arg>${INPUT_EMAIL}</arg>
<arg>${EMAIL}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-08-08T09:57:28.112130" elapsed="0.142696"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-08-08T09:57:28.259129" level="INFO">Typing text '<EMAIL>' into text field '//input[@id="usernameEntry"]'.</msg>
<arg>${INPUT_EMAIL_2}</arg>
<arg>${EMAIL}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-08-08T09:57:28.258374" elapsed="0.127687"/>
</kw>
<arg>'${result[0]}' == 'FAIL'</arg>
<arg>Input Text</arg>
<arg>${INPUT_EMAIL_2}</arg>
<arg>${EMAIL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-08-08T09:57:28.255358" elapsed="0.131017"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-08-08T09:57:28.387494" level="INFO">Clicking button '//button[@data-testid="primaryButton"]'.</msg>
<arg>${NEXT_BUTTON}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-08-08T09:57:28.386813" elapsed="0.053638"/>
</kw>
<arg>${EMAIL}</arg>
<status status="PASS" start="2025-08-08T09:57:28.109159" elapsed="0.332181"/>
</kw>
<kw name="Use Password" owner="Login">
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-08-08T09:57:29.444206" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-08-08T09:57:28.443221" elapsed="1.002757"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${OTHER_WAY}</arg>
<arg>5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-08-08T09:57:29.448682" elapsed="0.066852"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-08-08T09:57:29.519880" level="INFO">Clicking element '//span[@role="button"]'.</msg>
<arg>${OTHER_WAY}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-08-08T09:57:29.517622" elapsed="0.063742"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-08-08T09:57:30.583136" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-08-08T09:57:29.581984" elapsed="1.002603"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${USE_PASSWORD}</arg>
<arg>5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-08-08T09:57:30.586690" elapsed="0.036801"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-08-08T09:57:30.625835" level="INFO">Clicking element '//span[contains(text(), "Use your password")]'.</msg>
<arg>${USE_PASSWORD}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-08-08T09:57:30.624579" elapsed="0.104852"/>
</kw>
<status status="PASS" start="2025-08-08T09:57:28.442402" elapsed="2.287425"/>
</kw>
<kw name="Input Password" owner="Login">
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-08-08T09:57:32.733589" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-08-08T09:57:30.732055" elapsed="2.002127"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-08-08T09:57:32.737966" level="INFO">Typing text 'viet2007dz' into text field '//input[@id="passwordEntry"]'.</msg>
<arg>${INPUT_PASSWORD}</arg>
<arg>${PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-08-08T09:57:32.735994" elapsed="0.124869"/>
</kw>
<msg time="2025-08-08T09:57:32.861475" level="INFO">${result} = ('PASS', None)</msg>
<var>${result}</var>
<arg>Input Text</arg>
<arg>${INPUT_PASSWORD}</arg>
<arg>${PASSWORD}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-08-08T09:57:32.734932" elapsed="0.126743"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${result[0]}' == 'FAIL'</arg>
<arg>Input Text</arg>
<arg>${INPUT_PASSWORD_2}</arg>
<arg>${PASSWORD}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-08-08T09:57:32.862155" elapsed="0.001776"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-08-08T09:57:32.865172" level="INFO">Clicking button '//button[@data-testid="primaryButton"]'.</msg>
<arg>${NEXT_BUTTON}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-08-08T09:57:32.864505" elapsed="0.558611"/>
</kw>
<arg>${PASSWORD}</arg>
<status status="PASS" start="2025-08-08T09:57:30.730685" elapsed="2.693325"/>
</kw>
<kw name="Not Stay Login" owner="Login">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${NOT_STAY_LOGIN}</arg>
<arg>5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-08-08T09:57:33.426236" elapsed="0.235979"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-08-08T09:57:33.663406" level="INFO">Clicking button '//button[@data-testid="secondaryButton"]'.</msg>
<arg>${NOT_STAY_LOGIN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
