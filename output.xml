<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3.1 (Python 3.10.18 on linux)" generated="2025-08-07T15:38:55.240634" rpa="false" schemaversion="5">
<suite id="s1" name="Run" source="/home/<USER>/Desktop/project_local/reward_bing/Run">
<suite id="s1-s1" name="Login" source="/home/<USER>/Desktop/project_local/reward_bing/Run/Login.robot">
<test id="s1-s1-t1" name="Verify Login" line="10">
<kw name="Open Browser Keywords" owner="BrowserSetup">
<kw name="Get Chrome Options" owner="browser_options">
<msg time="2025-08-07T15:38:55.370990" level="INFO">Created temporary user data directory: /tmp/chrome_profile_j5ozpu27</msg>
<msg time="2025-08-07T15:38:55.371951" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x7ba5646fbd60&gt;</msg>
<var>${chrome_options}</var>
<doc>Configure Chrome browser options for Docker/headless environments.</doc>
<status status="PASS" start="2025-08-07T15:38:55.370402" elapsed="0.001709"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-08-07T15:38:55.373085" level="INFO">Opening browser 'Chrome' to base url 'https://rewards.bing.com/'.</msg>
<arg>${BASE_URL}</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-08-07T15:38:55.372472" elapsed="3.834671"/>
</kw>
<doc>Open the browser</doc>
<status status="PASS" start="2025-08-07T15:38:55.369574" elapsed="3.838258"/>
</kw>
<kw name="Login User" owner="Login">
<kw name="Input Email" owner="Login">
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-08-07T15:38:59.211750" level="INFO">Typing text '<EMAIL>' into text field '//input[@id="i0116"]'.</msg>
<msg time="2025-08-07T15:38:59.330666" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-3.png"&gt;&lt;img src="selenium-screenshot-3.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-08-07T15:38:59.330981" level="FAIL">Element with locator '//input[@id="i0116"]' not found.</msg>
<arg>${INPUT_EMAIL}</arg>
<arg>${EMAIL}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="FAIL" start="2025-08-07T15:38:59.211026" elapsed="0.120691">Element with locator '//input[@id="i0116"]' not found.</status>
</kw>
<msg time="2025-08-07T15:38:59.332023" level="INFO">${result} = ('FAIL', 'Element with locator \'//input[@id="i0116"]\' not found.')</msg>
<var>${result}</var>
<arg>Input Text</arg>
<arg>${INPUT_EMAIL}</arg>
<arg>${EMAIL}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-08-07T15:38:59.210754" elapsed="0.121397"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-08-07T15:38:59.333672" level="INFO">Typing text '<EMAIL>' into text field '//input[@id="usernameEntry"]'.</msg>
<arg>${INPUT_EMAIL_2}</arg>
<arg>${EMAIL}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-08-07T15:38:59.333229" elapsed="0.127022"/>
</kw>
<arg>'${result[0]}' == 'FAIL'</arg>
<arg>Input Text</arg>
<arg>${INPUT_EMAIL_2}</arg>
<arg>${EMAIL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-08-07T15:38:59.332419" elapsed="0.128159"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-08-07T15:38:59.461292" level="INFO">Clicking button '//button[@data-testid="primaryButton"]'.</msg>
<arg>${NEXT_BUTTON}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-08-07T15:38:59.460778" elapsed="0.063867"/>
</kw>
<arg>${EMAIL}</arg>
<status status="PASS" start="2025-08-07T15:38:59.210072" elapsed="0.315293"/>
</kw>
<kw name="Use Password" owner="Login">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-08-07T15:38:59.527402" level="INFO">Clicking element '//span[@role="button"]'.</msg>
<arg>${OTHER_WAY}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-08-07T15:38:59.526792" elapsed="2.004391"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<msg time="2025-08-07T15:39:06.853990" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-4.png"&gt;&lt;img src="selenium-screenshot-4.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-08-07T15:39:06.854336" level="FAIL">Element '//span[contains(text(), "Use your password")]' not visible after 5 seconds.</msg>
<arg>${USE_PASSWORD}</arg>
<arg>5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="FAIL" start="2025-08-07T15:39:01.531685" elapsed="5.323641">Element '//span[contains(text(), "Use your password")]' not visible after 5 seconds.</status>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${USE_PASSWORD}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-08-07T15:39:06.856115" elapsed="0.000595"/>
</kw>
<status status="FAIL" start="2025-08-07T15:38:59.525997" elapsed="7.331169">Element '//span[contains(text(), "Use your password")]' not visible after 5 seconds.</status>
</kw>
<kw name="Input Password" owner="Login">
<arg>${PASSWORD}</arg>
<status status="NOT RUN" start="2025-08-07T15:39:06.857975" elapsed="0.000329"/>
</kw>
<kw name="Not Stay Login" owner="Login">
<status status="NOT RUN" start="2025-08-07T15:39:06.858691" elapsed="0.000148"/>
</kw>
<arg>${USERNAME}</arg>
<arg>${PASSWORD}</arg>
<status status="FAIL" start="2025-08-07T15:38:59.209004" elapsed="7.650027">Element '//span[contains(text(), "Use your password")]' not visible after 5 seconds.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-08-07T15:39:06.859611" elapsed="0.000188"/>
</kw>
<doc>Test case to verify valid login with user admin</doc>
<status status="FAIL" start="2025-08-07T15:38:55.368534" elapsed="11.491716">Element '//span[contains(text(), "Use your password")]' not visible after 5 seconds.</status>
</test>
<status status="FAIL" start="2025-08-07T15:38:55.261395" elapsed="11.599854"/>
</suite>
<status status="FAIL" start="2025-08-07T15:38:55.243534" elapsed="11.619518"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="1" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="Run" id="s1" pass="0" fail="1" skip="0">Run</stat>
<stat name="Login" id="s1-s1" pass="0" fail="1" skip="0">Run.Login</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
