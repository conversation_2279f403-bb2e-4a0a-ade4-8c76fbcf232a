<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3.1 (Python 3.10.18 on linux)" generated="2025-08-07T15:38:02.371677" rpa="false" schemaversion="5">
<suite id="s1" name="Robot Interactive Console" source="/home/<USER>/.vscode/extensions/robocorp.robotframework-lsp-1.13.0/src/robotframework_ls/vendored/robotframework_interactive/robot_interactive_console.robot">
<test id="s1-t1" name="Default Task/Test" line="5">
<kw name="Interpreter Main Loop" owner="MainLoop">
<status status="PASS" start="2025-08-07T15:38:02.384240" elapsed="9.861411"/>
</kw>
<status status="PASS" start="2025-08-07T15:38:02.383870" elapsed="9.861976"/>
</test>
<status status="PASS" start="2025-08-07T15:38:02.372346" elapsed="9.874027"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="Robot Interactive Console" id="s1" pass="1" fail="0" skip="0">Robot Interactive Console</stat>
</suite>
</statistics>
<errors>
<msg time="2025-08-07T15:38:02.369632" level="WARN">Error in file '/home/<USER>/.vscode/extensions/robocorp.robotframework-lsp-1.13.0/src/robotframework_ls/vendored/robotframework_interactive/robot_interactive_console.robot' on line 4: Singular section headers like '*** Test Case ***' are deprecated. Use plural format like '*** Test Cases ***' instead.</msg>
</errors>
</robot>
