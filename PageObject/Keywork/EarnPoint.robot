*** Settings ***
Library    SeleniumLibrary
Resource   ../Locator/EarnPoint.robot


*** Keywords ***
Get All Earn Card Element
    [Arguments]     ${ELEMENT_CARD}
    ${elements}=    Get WebElements    ${ELEMENT_CARD}
    [Return]        ${elements}



Click All Earn 5 Links
    [Arguments]     ${ELEMENT_CARD}
    ${elements}=    Get All Earn Card Element     ${ELEMENT_CARD}
    FOR    ${index}    IN RANGE    ${len(${elements})}
        ${original_handle}=    Get Window Handle
        ${links}=    Get WebElements    //a//span[text()="5"]
        Click Element    ${links}[${index}]
        Sleep    1s
        ${all_handles}=    Get Window Handles
        FOR    ${handle}    IN    @{all_handles}
            Run Keyword If    '${handle}' != '${original_handle}'    Switch Window    ${handle}
        END
        Sleep    1s
        Close Window
        Switch Window    ${original_handle}
        Sleep    1s
    END




