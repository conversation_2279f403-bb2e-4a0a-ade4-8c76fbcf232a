*** Settings ***
Library    SeleniumLibrary
Resource   ../Locator/Login.robot


*** Keywords ***
Input Email
    [Arguments]    ${EMAIL}
    ${result}=    Run Keyword And Ignore Error    Input Text    ${INPUT_EMAIL}    ${EMAIL}
    Run Keyword If    '${result[0]}' == 'FAIL'    Input Text    ${INPUT_EMAIL_2}    ${EMAIL}
    Click Button     ${NEXT_BUTTON}


Input Password
    [Arguments]    ${PASSWORD}
    Sleep     2s
    ${result}=    Run Keyword And Ignore Error    Input Text    ${INPUT_PASSWORD}   ${PASSWORD}
    Run Keyword If    '${result[0]}' == 'FAIL'    Input Text    ${INPUT_PASSWORD_2}    ${PASSWORD}
    Click Button     ${NEXT_BUTTON}


Use Password
    Sleep     1s
    Wait Until Element Is Visible     ${OTHER_WAY}     5s
    Click Element    ${OTHER_WAY}
    Sleep     1s
    Wait Until Element Is Visible     ${USE_PASSWORD}     5s
    Click Element    ${USE_PASSWORD}


Not Stay Login
    Wait Until Element Is Visible     ${NOT_STAY_LOGIN}     5s
    Click Button     ${NOT_STAY_LOGIN}


Login User
    [Arguments]    ${EMAIL}  ${PASSWORD}
    Input Email    ${EMAIL}
    Use Password
    Input Password    ${PASSWORD}
    Not Stay Login

