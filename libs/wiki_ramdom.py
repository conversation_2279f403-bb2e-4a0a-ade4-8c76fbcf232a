import wikipedia


def get_random_wiki_titles(n):
    wikipedia.set_lang("vi")  # Sử dụng Wikipedia tiếng Việt
    try:
        titles = wikipedia.random(pages=n)
        # Nếu n == 1, wikipedia.random trả về string, nên cầ<PERSON> về list
        if isinstance(titles, str):
            titles = [titles]
        return titles
    except Exception as e:
        print("Lỗi:", e)
        return []


if __name__ == "__main__":
    n = int(input("Nhập số lượng từ khóa cần tạo: "))
    keywords = get_random_wiki_titles(n)
    print("<PERSON>h sách từ khóa ngẫu nhiên:", keywords)
