# variables/browser_options.py
import os
import shutil
import atexit
from selenium.webdriver.chrome.options import Options
import tempfile

_temp_user_data_dir = None


def get_chrome_options():
    """
    Configure Chrome browser options for Docker/headless environments.
    """
    global _temp_user_data_dir

    # Clean up the old temporary directory if it exists
    if _temp_user_data_dir and os.path.exists(_temp_user_data_dir):
        try:
            _cleanup_temp_dir_function()
        except Exception as e:
            print(f"Warning: Could not cleanup stale temp directory {_temp_user_data_dir}: {e}")
            _temp_user_data_dir = None

    # Create a new temporary directory if it doesn't exist
    if not _temp_user_data_dir:
        _temp_user_data_dir = tempfile.mkdtemp(prefix="chrome_profile_")
        atexit.register(_cleanup_temp_dir_function)
        print(f"Created temporary user data directory: {_temp_user_data_dir}")

    options = Options()

    # Check if running in Docker or Jenkins
    is_docker = os.path.exists('/.dockerenv') or os.environ.get('DOCKER_ENV') == 'true'
    is_jenkins = os.environ.get('JENKINS_ENV') == 'true'

    # Basic options for Docker/headless environments
    if is_docker or is_jenkins:
        options.add_argument("--headless=new")  # New headless mode

    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    options.add_argument("--disable-software-rasterizer")
    options.add_argument("--window-size=1920,1080")
    options.add_argument("--remote-debugging-port=0")  # Use random port
    options.add_argument(f"--user-data-dir={_temp_user_data_dir}")  # Temporary user data directory

    # Additional options for Docker
    if is_docker:
        options.add_argument("--single-process")
        options.add_argument("--no-zygote")
        options.add_argument("--disable-background-networking")
        options.add_argument("--disable-sync")
        options.add_argument("--disable-translate")
        options.add_argument("--mute-audio")
        options.add_argument("--no-first-run")
        options.add_argument("--disable-logging")
        options.add_argument("--disable-component-update")
        options.add_argument("--disable-client-side-phishing-detection")
        options.add_argument("--disable-hang-monitor")
        options.add_argument("--disable-prompt-on-repost")
        options.add_argument("--disable-domain-reliability")
        options.add_argument("--aggressive-cache-discard")

    # Security and stability options
    options.add_argument("--disable-web-security")
    options.add_argument("--disable-features=VizDisplayCompositor")
    options.add_argument("--disable-notifications")
    options.add_argument("--disable-extensions")
    options.add_argument("--disable-plugins")
    options.add_argument("--disable-default-apps")
    options.add_argument("--ignore-ssl-errors=yes")
    options.add_argument("--allow-insecure-localhost")
    options.add_argument("--ignore-certificate-errors")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument("--safebrowsing-disable-download-protection")

    # User options
    options.add_argument(
        '--user-agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

    # Experimental options
    exclude_switches = ["enable-automation", "enable-logging"]
    if is_docker:
        exclude_switches.append("enable-blink-features")

    options.add_experimental_option("excludeSwitches", exclude_switches)
    options.add_experimental_option('useAutomationExtension', False)
    options.add_experimental_option('prefs', {
        "credentials_enable_service": False,
        "profile.password_manager_enabled": False,
        "profile.default_content_settings.popups": 0,
        "profile.default_content_setting_values.notifications": 2,
        "profile.managed_default_content_settings.images": 2,
        "profile.default_content_setting_values.media_stream_mic": 2,
        "profile.default_content_setting_values.media_stream_camera": 2,
        "profile.default_content_setting_values.geolocation": 2,
        "profile.default_content_setting_values.desktop_notification": 2
    })

    return options


def _cleanup_temp_dir_function():
    """
    Clean up the temporary user data directory.
    """
    global _temp_user_data_dir
    if _temp_user_data_dir and os.path.exists(_temp_user_data_dir):
        print(f"Attempting to cleanup temporary user data directory: {_temp_user_data_dir}")
        try:
            shutil.rmtree(_temp_user_data_dir, ignore_errors=True)
            print(f"Successfully cleaned up temporary user data directory: {_temp_user_data_dir}")
        except Exception as e:
            print(f"ERROR: Failed to cleanup temp directory {_temp_user_data_dir}: {e}")
    _temp_user_data_dir = None


def cleanup_chrome_user_data_directory():
    """
    Robot Framework keyword to clean up the Chrome user data directory.
    """
    _cleanup_temp_dir_function()
