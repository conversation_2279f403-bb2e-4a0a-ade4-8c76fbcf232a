import json
import time


def load_local_storage(driver):
    local_storage = {
        "_uetsid": "3e55c020733411f0b4ac2583bdd9b967",
        "_uetsid_exp": "Fri, 08 Aug 2025 02:32:45 GMT",
        "_uetvid": "c89c4ea05c7c11f08c2df325ebb87865",
        "_uetvid_exp": "Tue, 01 Sep 2026 02:32:45 GMT",
        "redDotFormCodeTimeStamps": 1754532878879,
        "redDotlastRedDotClickedTime": 1754532878879
    }

    for key, value in local_storage.items():
        driver.execute_script(f"window.localStorage.setItem('{key}', {json.dumps(value)});")

    time.sleep(3)
    driver.refresh()
    driver.get(
        'https://rewards.bing.com/')


def set_cookies(driver):
    cookies = [
        {"name": "_C_Auth", "value": "", "domain": "rewards.bing.com", "path": "/", "expiry": None},
        {"name": "_C_ETH", "value": "1", "domain": ".bing.com", "path": "/", "expiry": None},
        {"name": "_clck", "value": "qrgvs9%7C2%7Cfy9%7C1%7C2016", "domain": ".bing.com", "path": "/",
         "expiry": 1786116867},
        {"name": "_clck", "value": "syqk7u%7C2%7Cfy9%7C1%7C1996", "domain": ".microsoft.com", "path": "/",
         "expiry": 1786114775},
        {"name": "_clsk", "value": "13gig1u%7C1754530954513%7C6%7C1%7Cv.clarity.ms%2Fcollect",
         "domain": ".microsoft.com", "path": "/", "expiry": 1754607754},
        {"name": "_clsk", "value": "12ty63r%7C1754534639571%7C9%7C0%7Cn.clarity.ms%2Fcollect", "domain": ".bing.com",
         "path": "/", "expiry": 1754611439},
        {"name": "_DPC", "value": "U536_rwdamc", "domain": ".bing.com", "path": "/", "expiry": None},
        {"name": "_EDGE_S", "value": "SID=0A8A390676CD6956085E2F47774868DF", "domain": ".bing.com", "path": "/",
         "expiry": None},
        {"name": "_fbp", "value": "fb.1.1754464583243.540559663198060807", "domain": ".microsoft.com", "path": "/",
         "expiry": 1757072475},
        {"name": "_gcl_au", "value": "1.1.911838259.1752035265", "domain": ".microsoft.com", "path": "/",
         "expiry": 1759817265},
        {"name": "_NTPC", "value": "U536", "domain": ".bing.com", "path": "/", "expiry": None},
        {"name": "_RwBf", "value": "mta=0&rc=410&rb=410&...", "domain": ".bing.com", "path": "/", "expiry": 1789946925},
        {"name": "_SS", "value": "PC=U536&SID=0A8A390676CD6956085E2F47774868DF&BCEX=1", "domain": ".bing.com",
         "path": "/", "expiry": None},
        {"name": "_uetsid", "value": "3e55c020733411f0b4ac2583bdd9b967", "domain": ".bing.com", "path": "/",
         "expiry": 1754611439},
        {"name": "_uetsid", "value": "3244c690728e11f0a44847abd5f8f1b6|24w6|2|fy9|0|2044", "domain": ".microsoft.com",
         "path": "/", "expiry": 1754607677},
        {"name": "_uetvid", "value": "c89c4ea05c7c11f08c2df325ebb87865", "domain": ".bing.com", "path": "/",
         "expiry": 1788319439},
        {"name": "_uetvid", "value": "3244bed0728e11f0a1a96108325a7599", "domain": ".microsoft.com", "path": "/",
         "expiry": 1788316877},
        {"name": ".AspNetCore.Antiforgery.icPscOZlg04", "value": "CfDJ8OmjzpJHIp5...", "domain": "rewards.bing.com",
         "path": "/", "expiry": None},
        {"name": "ak_bmsc", "value": "BB732B7876B76E4357A61AEA4326F031~...", "domain": ".microsoft.com", "path": "/",
         "expiry": 1754614873},
        {"name": "akacd_OneRF", "value": "1762240581~rv=91~id=f6cbc68a4b73f87e44ea2d612497f428",
         "domain": "www.microsoft.com", "path": "/", "expiry": 1757018181},
        {"name": "AMCV_EA76ADE95776D2EC7F000101%40AdobeOrg", "value": "1585540135%7CMCIDTS%7C20307%7C...",
         "domain": ".microsoft.com", "path": "/", "expiry": 1788788075},
        {"name": "ANON", "value": "A=3D7E03ECA141371B4B316060FFFFFFFF&E=1f5a&W=3", "domain": ".clarity.ms", "path": "/",
         "expiry": 1761068748},
        {"name": "ANON", "value": "A=3D7E03ECA141371B4B316060FFFFFFFF&E=1f5a&W=3", "domain": ".microsoft.com",
         "path": "/", "expiry": 1761066250},
        {"name": "ANON", "value": "A=201E16E7CCBF820EC3504C85FFFFFFFF&E=1f75&W=2", "domain": ".live.com", "path": "/",
         "expiry": 1771691726},
    ]

    # Add each cookie
    for cookie in cookies:
        try:
            c = {
                "name": cookie["name"],
                "value": cookie["value"],
                "domain": cookie["domain"],
                "path": cookie["path"],
            }
            if cookie.get("expiry"):
                c["expiry"] = int(cookie["expiry"])
            driver.add_cookie(c)
        except Exception as e:
            print(f"Failed to add cookie: {cookie['name']} - {e}")
    time.sleep(3)
    driver.refresh()
    driver.get(
        'https://rewards.bing.com/')